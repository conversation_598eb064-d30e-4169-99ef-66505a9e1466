{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "supply-chain-security-2024", "title": "Supply Chain Security Attacks & Defense", "description": "Real-world supply chain attacks, dependency vulnerabilities, and practical defense strategies based on recent incidents like SolarWinds, Codecov, and npm package compromises.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T11:00:00Z", "tags": ["supply-chain", "dependencies", "npm", "solarwinds", "codecov", "practical", "devsecops"], "passing_score_percentage": 80, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "solarwinds_attack_vector", "type": "multiple_choice", "text": "The SolarWinds attack (2020) affected 18,000+ organizations. What was the **primary attack vector** that made this supply chain attack so devastating?", "points": 2, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Compromised software update mechanism with digitally signed malicious updates", "is_correct": true, "feedback": "Correct! The attackers compromised the build system and distributed signed malicious updates."}, {"id": "opt2", "text": "SQL injection in the SolarWinds customer portal", "is_correct": false, "feedback": "While there were portal vulnerabilities, the main attack was through the software supply chain."}, {"id": "opt3", "text": "Phishing emails targeting SolarWinds employees", "is_correct": false, "feedback": "Phishing may have been used for initial access, but the impact came from the supply chain compromise."}, {"id": "opt4", "text": "Zero-day vulnerability in the Orion platform", "is_correct": false, "feedback": "The attack didn't rely on zero-days but on compromising the legitimate software distribution."}], "hint": [{"text": "Think about how the malicious code reached thousands of organizations simultaneously.", "delay_seconds": 30}, {"text": "The key was that customers trusted and installed what appeared to be legitimate software updates.", "delay_seconds": 60}], "feedback_correct": "Excellent! The signed malicious updates made this attack particularly effective and hard to detect.", "feedback_incorrect": "The primary vector was compromising the software build/update process to distribute signed malicious code.", "explanation": "**SolarWinds Supply Chain Attack Analysis:**\\n\\n**Attack Timeline & Methodology:**\\n- **March 2020**: Initial compromise of SolarWinds build environment\\n- **March-June 2020**: SUNBURST backdoor inserted into Orion software\\n- **June 2020**: Malicious updates distributed to 18,000+ customers\\n- **December 2020**: Attack discovered by FireEye\\n\\n**Technical Details:**\\n- **SUNBURST Backdoor**: Dormant for 12-14 days, then activated\\n- **Command & Control**: Used legitimate domains to blend in\\n- **Digital Signatures**: Valid SolarWinds certificates made detection difficult\\n- **Selective Targeting**: Only activated for high-value targets\\n\\n**Why It Was So Effective:**\\n1. **Trust**: Customers trusted signed updates from legitimate vendor\\n2. **Scale**: Single compromise affected thousands of organizations\\n3. **Stealth**: Backdoor designed to avoid detection\\n4. **Persistence**: Embedded in legitimate software updates\\n\\n**Detection Indicators:**\\n```bash\\n# Check for SUNBURST indicators\\n# DNS queries to avsvmcloud.com\\n# Registry key: HKEY_LOCAL_MACHINE\\\\SOFTWARE\\\\Microsoft\\\\\\n# File hashes: 32519b85c0b422e4656de6e6c41878e95fd95026\\n```\\n\\n**Prevention Strategies:**\\n1. **Software Bill of Materials (SBOM)**: Track all components\\n2. **Code Signing Verification**: Validate signatures and certificates\\n3. **Network Monitoring**: Monitor for unusual outbound connections\\n4. **Zero Trust**: Don't automatically trust vendor software\\n5. **Incident Response**: Prepare for supply chain compromises", "single_correct_answer": true}, {"question_id": "npm_package_typosquatting", "type": "short_answer", "text": "A developer accidentally installs a malicious npm package 'reqeust' instead of 'request'. What npm command should they run to **check for known vulnerabilities** in their installed packages?", "points": 1, "difficulty": "beginner", "correct_answers": ["npm audit", "npm audit --audit-level high", "npm audit --audit-level moderate", "npm audit fix"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "npm has a built-in command to check for security vulnerabilities in dependencies.", "delay_seconds": 25}, {"text": "The command is 'npm audit' - it checks packages against the npm security advisory database.", "delay_seconds": 50}], "feedback_correct": "Correct! npm audit checks installed packages against known vulnerability databases.", "feedback_incorrect": "The correct command is 'npm audit' to check for known vulnerabilities in dependencies.", "explanation": "**npm Package Security & Typosquatting:**\\n\\n**Typosquatting Attack Pattern:**\\n- **Target**: Popular packages like 'request', 'lodash', 'express'\\n- **Method**: Register similar names ('reqeust', 'loadash', 'expres')\\n- **Payload**: Malicious code that steals credentials, installs backdoors\\n- **Distribution**: Developers make typos during installation\\n\\n**Real-World Examples:**\\n- **event-stream (2018)**: 2M+ weekly downloads, Bitcoin wallet stealer\\n- **eslint-scope (2018)**: Credential harvesting malware\\n- **ua-parser-js (2021)**: Cryptocurrency miner and password stealer\\n\\n**npm Security Commands:**\\n```bash\\n# Check for vulnerabilities\\nnpm audit\\n\\n# Fix automatically fixable issues\\nnpm audit fix\\n\\n# Force fixes (potentially breaking)\\nnpm audit fix --force\\n\\n# Check specific severity\\nnpm audit --audit-level high\\n\\n# Generate detailed report\\nnpm audit --json\\n```\\n\\n**Prevention Best Practices:**\\n1. **Package Lock Files**: Use package-lock.json or yarn.lock\\n2. **Dependency Scanning**: Integrate npm audit into CI/CD\\n3. **Manual Review**: Check package names carefully\\n4. **Trusted Sources**: Verify package maintainers and download counts\\n5. **Regular Updates**: Keep dependencies updated but test thoroughly\\n6. **SBOM Generation**: Maintain software bill of materials"}]}}