{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "api-security-testing-practical", "title": "API Security Testing - Practical Scenarios", "description": "Hands-on API security testing covering REST, GraphQL, authentication flaws, and business logic vulnerabilities.", "author": "QuizFlow Security Team", "creation_date": "2025-05-25T19:58:38.981Z", "tags": ["api-security", "rest-api", "graphql", "authentication", "business-logic"], "passing_score_percentage": 75, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "api-security-testing-practical_q1", "type": "multiple_choice", "text": "Sample question 1 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 1 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}, {"question_id": "api-security-testing-practical_q2", "type": "multiple_choice", "text": "Sample question 2 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 2 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}, {"question_id": "api-security-testing-practical_q3", "type": "multiple_choice", "text": "Sample question 3 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 3 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}, {"question_id": "api-security-testing-practical_q4", "type": "multiple_choice", "text": "Sample question 4 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 4 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}, {"question_id": "api-security-testing-practical_q5", "type": "multiple_choice", "text": "Sample question 5 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 5 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}, {"question_id": "api-security-testing-practical_q6", "type": "multiple_choice", "text": "Sample question 6 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 6 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}, {"question_id": "api-security-testing-practical_q7", "type": "multiple_choice", "text": "Sample question 7 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 7 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}, {"question_id": "api-security-testing-practical_q8", "type": "multiple_choice", "text": "Sample question 8 for API Security Testing - Practical Scenarios", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 8 in API Security Testing - Practical Scenarios.", "single_correct_answer": true}]}}