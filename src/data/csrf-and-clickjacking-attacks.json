{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "csrf-and-clickjacking-attacks", "title": "CSRF & Clickjacking Attack Vectors", "description": "Cross-Site Request Forgery and Clickjacking attacks with practical exploitation techniques and defense mechanisms.", "author": "QuizFlow Security Team", "creation_date": "2025-05-25T19:58:38.981Z", "tags": ["csrf", "clickjacking", "ui-redressing", "token-bypass", "same-site"], "passing_score_percentage": 75, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "csrf-and-clickjacking-attacks_q1", "type": "multiple_choice", "text": "Sample question 1 for CSRF & Clickjacking Attack Vectors", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 1 in CSRF & Clickjacking Attack Vectors.", "single_correct_answer": true}, {"question_id": "csrf-and-clickjacking-attacks_q2", "type": "multiple_choice", "text": "Sample question 2 for CSRF & Clickjacking Attack Vectors", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 2 in CSRF & Clickjacking Attack Vectors.", "single_correct_answer": true}, {"question_id": "csrf-and-clickjacking-attacks_q3", "type": "multiple_choice", "text": "Sample question 3 for CSRF & Clickjacking Attack Vectors", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 3 in CSRF & Clickjacking Attack Vectors.", "single_correct_answer": true}, {"question_id": "csrf-and-clickjacking-attacks_q4", "type": "multiple_choice", "text": "Sample question 4 for CSRF & Clickjacking Attack Vectors", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 4 in CSRF & Clickjacking Attack Vectors.", "single_correct_answer": true}, {"question_id": "csrf-and-clickjacking-attacks_q5", "type": "multiple_choice", "text": "Sample question 5 for CSRF & Clickjacking Attack Vectors", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 5 in CSRF & Clickjacking Attack Vectors.", "single_correct_answer": true}, {"question_id": "csrf-and-clickjacking-attacks_q6", "type": "multiple_choice", "text": "Sample question 6 for CSRF & Clickjacking Attack Vectors", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 6 in CSRF & Clickjacking Attack Vectors.", "single_correct_answer": true}, {"question_id": "csrf-and-clickjacking-attacks_q7", "type": "multiple_choice", "text": "Sample question 7 for CSRF & Clickjacking Attack Vectors", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 7 in CSRF & Clickjacking Attack Vectors.", "single_correct_answer": true}]}}