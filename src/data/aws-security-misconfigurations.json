{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "aws-security-misconfigurations", "title": "AWS Security Misconfigurations & Exploitation", "description": "Common AWS security misconfigurations, exploitation techniques, and practical remediation strategies.", "author": "QuizFlow Security Team", "creation_date": "2025-05-25T19:58:38.982Z", "tags": ["aws", "cloud-security", "misconfigurations", "s3", "iam", "ec2"], "passing_score_percentage": 75, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "aws-security-misconfigurations_q1", "type": "multiple_choice", "text": "Sample question 1 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 1 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q2", "type": "multiple_choice", "text": "Sample question 2 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 2 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q3", "type": "multiple_choice", "text": "Sample question 3 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 3 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q4", "type": "multiple_choice", "text": "Sample question 4 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 4 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q5", "type": "multiple_choice", "text": "Sample question 5 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 5 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q6", "type": "multiple_choice", "text": "Sample question 6 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 6 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q7", "type": "multiple_choice", "text": "Sample question 7 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 7 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q8", "type": "multiple_choice", "text": "Sample question 8 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 8 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q9", "type": "multiple_choice", "text": "Sample question 9 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 9 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q10", "type": "multiple_choice", "text": "Sample question 10 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 10 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q11", "type": "multiple_choice", "text": "Sample question 11 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 11 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}, {"question_id": "aws-security-misconfigurations_q12", "type": "multiple_choice", "text": "Sample question 12 for AWS Security Misconfigurations & Exploitation", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 12 in AWS Security Misconfigurations & Exploitation.", "single_correct_answer": true}]}}