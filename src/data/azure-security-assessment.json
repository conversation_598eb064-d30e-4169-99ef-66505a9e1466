{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "azure-security-assessment", "title": "Azure Security Assessment & Penetration Testing", "description": "Azure security assessment techniques, common vulnerabilities, and cloud penetration testing methodologies.", "author": "QuizFlow Security Team", "creation_date": "2025-05-25T19:58:38.983Z", "tags": ["azure", "cloud-security", "penetration-testing", "azure-ad", "storage-accounts"], "passing_score_percentage": 75, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "azure-security-assessment_q1", "type": "multiple_choice", "text": "Sample question 1 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 1 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q2", "type": "multiple_choice", "text": "Sample question 2 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 2 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q3", "type": "multiple_choice", "text": "Sample question 3 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 3 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q4", "type": "multiple_choice", "text": "Sample question 4 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 4 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q5", "type": "multiple_choice", "text": "Sample question 5 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 5 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q6", "type": "multiple_choice", "text": "Sample question 6 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 6 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q7", "type": "multiple_choice", "text": "Sample question 7 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 7 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q8", "type": "multiple_choice", "text": "Sample question 8 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 8 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q9", "type": "multiple_choice", "text": "Sample question 9 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 9 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}, {"question_id": "azure-security-assessment_q10", "type": "multiple_choice", "text": "Sample question 10 for Azure Security Assessment & Penetration Testing", "points": 1, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 10 in Azure Security Assessment & Penetration Testing.", "single_correct_answer": true}]}}