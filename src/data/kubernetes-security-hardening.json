{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "kubernetes-security-hardening", "title": "Kubernetes Security Hardening & Attack Vectors", "description": "Kubernetes security vulnerabilities, container escape techniques, and cluster hardening best practices.", "author": "QuizFlow Security Team", "creation_date": "2025-05-25T19:58:38.983Z", "tags": ["kubernetes", "container-security", "cluster-hardening", "rbac", "pod-security"], "passing_score_percentage": 80, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "kubernetes-security-hardening_q1", "type": "multiple_choice", "text": "Sample question 1 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 1 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q2", "type": "multiple_choice", "text": "Sample question 2 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 2 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q3", "type": "multiple_choice", "text": "Sample question 3 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 3 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q4", "type": "multiple_choice", "text": "Sample question 4 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 4 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q5", "type": "multiple_choice", "text": "Sample question 5 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 5 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q6", "type": "multiple_choice", "text": "Sample question 6 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 6 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q7", "type": "multiple_choice", "text": "Sample question 7 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 7 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q8", "type": "multiple_choice", "text": "Sample question 8 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 8 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}, {"question_id": "kubernetes-security-hardening_q9", "type": "multiple_choice", "text": "Sample question 9 for Kubernetes Security Hardening & Attack Vectors", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 9 in Kubernetes Security Hardening & Attack Vectors.", "single_correct_answer": true}]}}