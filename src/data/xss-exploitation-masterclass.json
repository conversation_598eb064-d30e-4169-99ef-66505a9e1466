{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "xss-exploitation-masterclass", "title": "XSS Exploitation Masterclass - From Basic to Advanced", "description": "Complete XSS exploitation guide covering reflected, stored, DOM-based, and advanced XSS techniques with real-world payloads.", "author": "QuizFlow Security Team", "creation_date": "2025-05-25T19:58:38.980Z", "tags": ["xss", "dom-xss", "stored-xss", "reflected-xss", "payload-crafting"], "passing_score_percentage": 80, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "xss-exploitation-masterclass_q1", "type": "multiple_choice", "text": "Sample question 1 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 1 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q2", "type": "multiple_choice", "text": "Sample question 2 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 2 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q3", "type": "multiple_choice", "text": "Sample question 3 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 3 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q4", "type": "multiple_choice", "text": "Sample question 4 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 4 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q5", "type": "multiple_choice", "text": "Sample question 5 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 5 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q6", "type": "multiple_choice", "text": "Sample question 6 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 6 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q7", "type": "multiple_choice", "text": "Sample question 7 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 7 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q8", "type": "multiple_choice", "text": "Sample question 8 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 8 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q9", "type": "multiple_choice", "text": "Sample question 9 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 9 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}, {"question_id": "xss-exploitation-masterclass_q10", "type": "multiple_choice", "text": "Sample question 10 for XSS Exploitation Masterclass - From Basic to Advanced", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 10 in XSS Exploitation Masterclass - From Basic to Advanced.", "single_correct_answer": true}]}}