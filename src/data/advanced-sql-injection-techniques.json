{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-sql-injection-techniques", "title": "Advanced SQL Injection Techniques & Bypass Methods", "description": "Advanced SQL injection techniques including blind SQLi, time-based attacks, WAF bypass methods, and NoSQL injection scenarios.", "author": "QuizFlow Security Team", "creation_date": "2025-05-25T19:58:38.980Z", "tags": ["sql-injection", "blind-sqli", "waf-bypass", "nosql", "database-security"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "advanced-sql-injection-techniques_q1", "type": "multiple_choice", "text": "Sample question 1 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 1 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q2", "type": "multiple_choice", "text": "Sample question 2 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 2 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q3", "type": "multiple_choice", "text": "Sample question 3 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 3 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q4", "type": "multiple_choice", "text": "Sample question 4 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 4 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q5", "type": "multiple_choice", "text": "Sample question 5 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 5 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q6", "type": "multiple_choice", "text": "Sample question 6 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 6 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q7", "type": "multiple_choice", "text": "Sample question 7 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 7 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q8", "type": "multiple_choice", "text": "Sample question 8 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 8 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q9", "type": "multiple_choice", "text": "Sample question 9 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 9 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q10", "type": "multiple_choice", "text": "Sample question 10 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 10 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q11", "type": "multiple_choice", "text": "Sample question 11 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 11 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}, {"question_id": "advanced-sql-injection-techniques_q12", "type": "multiple_choice", "text": "Sample question 12 for Advanced SQL Injection Techniques & Bypass Methods", "points": 1, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Option A", "is_correct": false}, {"id": "opt2", "text": "Option B", "is_correct": true}, {"id": "opt3", "text": "Option C", "is_correct": false}, {"id": "opt4", "text": "Option D", "is_correct": false}], "feedback_correct": "Correct!", "feedback_incorrect": "Incorrect.", "explanation": "Detailed explanation for question 12 in Advanced SQL Injection Techniques & Bypass Methods.", "single_correct_answer": true}]}}